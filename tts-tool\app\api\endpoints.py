"""
API端点定义
"""
import os
import asyncio
import logging
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, BackgroundTasks
from fastapi.responses import FileResponse, JSONResponse
from fastapi.concurrency import run_in_threadpool

from app.models.schemas import (
    TTSRequest, 
    TTSResponse, 
    VoicesResponse, 
    StatusResponse, 
    ErrorResponse
)
from app.core.tts_engine import tts_engine
from app.core.config import get_settings

logger = logging.getLogger(__name__)
router = APIRouter()
settings = get_settings()

# 并发控制
_active_requests = 0
_max_concurrent = settings.max_concurrent_requests


async def _check_concurrency():
    """检查并发请求数量"""
    global _active_requests
    if _active_requests >= _max_concurrent:
        raise HTTPException(
            status_code=429,
            detail="服务器繁忙，请稍后重试"
        )


async def _increment_requests():
    """增加活跃请求计数"""
    global _active_requests
    _active_requests += 1


async def _decrement_requests():
    """减少活跃请求计数"""
    global _active_requests
    _active_requests = max(0, _active_requests - 1)


@router.post("/tts", response_model=TTSResponse)
async def text_to_speech(
    request: TTSRequest,
    background_tasks: BackgroundTasks
) -> FileResponse:
    """
    文本转语音接口
    
    Args:
        request: TTS请求参数
        background_tasks: 后台任务
        
    Returns:
        音频文件响应
    """
    await _check_concurrency()
    await _increment_requests()
    
    try:
        # 检查TTS引擎状态
        if not tts_engine.is_loaded:
            raise HTTPException(
                status_code=503,
                detail="TTS服务未就绪，请稍后重试"
            )
        
        logger.info(f"开始处理TTS请求: {request.text[:50]}...")
        
        # 执行语音合成
        audio_file, metadata = await tts_engine.synthesize(
            text=request.text,
            voice=request.voice,
            speed=request.speed,
            format=request.format
        )
        
        # 设置响应头
        headers = {
            "Content-Disposition": f"attachment; filename=tts_output.{request.format}",
            "X-Audio-Duration": str(metadata.get("duration", 0)),
            "X-Audio-Size": str(metadata.get("file_size", 0)),
            "X-Voice-Used": request.voice
        }
        
        # 添加后台任务清理文件
        background_tasks.add_task(_cleanup_file, audio_file)
        
        logger.info(f"TTS请求处理完成，文件: {audio_file}")
        
        return FileResponse(
            path=audio_file,
            media_type=f"audio/{request.format}",
            headers=headers
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"TTS请求处理失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"语音合成失败: {str(e)}"
        )
    finally:
        await _decrement_requests()


@router.get("/voices", response_model=VoicesResponse)
async def get_voices() -> VoicesResponse:
    """
    获取可用语音列表
    
    Returns:
        语音列表响应
    """
    try:
        voices = await tts_engine.get_available_voices()
        return VoicesResponse(voices=voices)
        
    except Exception as e:
        logger.error(f"获取语音列表失败: {e}")
        raise HTTPException(
            status_code=500,
            detail="获取语音列表失败"
        )


@router.get("/status", response_model=StatusResponse)
async def get_status() -> StatusResponse:
    """
    获取系统状态
    
    Returns:
        状态响应
    """
    try:
        voices = await tts_engine.get_available_voices()
        
        return StatusResponse(
            status="healthy" if tts_engine.is_loaded else "loading",
            model_loaded=tts_engine.is_loaded,
            version=settings.app_version,
            available_voices=len(voices)
        )
        
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        return StatusResponse(
            status="error",
            model_loaded=False,
            version=settings.app_version,
            available_voices=0
        )


@router.post("/test")
async def test_tts() -> Dict[str, Any]:
    """
    测试TTS功能
    
    Returns:
        测试结果
    """
    try:
        test_text = "你好，这是一个测试。"
        
        # 执行快速测试
        audio_file, metadata = await tts_engine.synthesize(
            text=test_text,
            voice="zf_001",  # 使用实际存在的中文女声
            speed=1.0,
            format="wav"
        )
        
        # 清理测试文件
        try:
            os.unlink(audio_file)
        except:
            pass
        
        return {
            "success": True,
            "message": "TTS测试成功",
            "duration": metadata.get("duration", 0),
            "voice_used": "zh_female_001"
        }
        
    except Exception as e:
        logger.error(f"TTS测试失败: {e}")
        return {
            "success": False,
            "message": f"TTS测试失败: {str(e)}"
        }


async def _cleanup_file(file_path: str):
    """清理临时文件"""
    try:
        # 延迟删除，确保文件传输完成
        await asyncio.sleep(60)
        if os.path.exists(file_path):
            os.unlink(file_path)
            logger.debug(f"已清理临时文件: {file_path}")
    except Exception as e:
        logger.warning(f"清理临时文件失败 {file_path}: {e}")


# 错误处理器将在main.py中定义

