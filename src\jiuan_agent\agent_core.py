"""
久安大模型代理核心模块 - 包含代理创建和函数调用功能
"""

import os
import re
import json
import configparser
import logging
import asyncio
import httpx
import time
from typing import Optional, Dict, Any, List

# 导入API工具函数
from .api_tools import (
    query_event_surround_resource,
    preview_camera,
    one_map_position_resource,
    event_stop_plan,
    event_start_plan,
    query_all_event_infos,
    search_emergency_plan,
    query_area_plans,
    query_resource_cameras,
    start_call_by_name,
    query_model_resources,
    start_real_time_travel,
    start_meeting,
    end_meeting
)

# 创建logger
logger = logging.getLogger("jiuan_agent")

# 全局变量存储access_token和过期时间
_access_token = None
_token_expires_at = 0

class JiuanFunctionCallingAgent:
    """久安大模型函数调用代理"""

    def __init__(self, config):
        self.config = config
        self.functions = self._define_functions()

    def _define_functions(self) -> List[Dict]:
        """定义可用的函数列表（简化版，去除可选参数，设置默认值）"""
        return [
            {
                "name": "query_event_surround_resource",
                "description": "查询事件周边的应急资源。resource_type为可选参数，可以是：队伍、仓库、专家、避难场所、医疗卫生、防护目标、企业信息、物资、装备、医院、危险源、视频，或者留空查询所有类型",
                "parameters": {
                    "event_name": "string",
                    "distance": "int",
                    "resource_type": "string|optional"
                }
            },
            {
                "name": "preview_camera",
                #"description": "预览摄像头",
                "parameters": {
                    "camera_id": "string"
                }
            },
            {
                "name": "one_map_position_resource",
                #"description": "一张图查看并定位资源",
                "parameters": {
                    "resource_name": "string"
                }
            },
            {
                "name": "event_stop_plan",
                #"description": "终止事件预案",
                "parameters": {
                    "event_name": "string"
                }
            },
            {
                "name": "event_start_plan",
                #"description": "启动事件预案",
                "parameters": {
                    "event_name": "string"
                }
            },
            {
                "name": "query_all_event_infos",
                #"description": "搜索事件信息",
                "parameters": {
                    "page": 1,
                    "page_size": 50
                }
            },
            {
                "name": "search_emergency_plan",
                #"description": "搜索应急预案",
                "parameters": {
                    "page": 1,
                    "page_size": 20
                }
            },
            {
                "name": "query_area_plans",
                #"description": "根据区域名称查询预案信息",
                "parameters": {
                    "area_name": "string"
                }
            },
            {
                "name": "query_resource_cameras",
                #"description": "查询资源周边的视频信息",
                "parameters": {
                    "resource_name": "string",
                    "resource_type": "string"
                }
            },
            {
                "name": "start_call_by_name",
                #"description": "根据队伍/人名发起指定的呼叫",
                "parameters": {
                    "resource_name": "string",
                    "resource_type": "string",
                    "calling_type": "string"
                }
            },
            {
                "name": "query_model_resources",
                #"description": "查询模型周边资源",
                "parameters": {
                    "model_name": "string"
                }
            },
            {
                "name": "start_real_time_travel",
                #"description": "启动实时轨迹",
                "parameters": {
                    "event_name": "string"
                }
            },
            {
                "name": "start_meeting",
                #"description": "开启会议",
                "parameters": {
                    "meeting_name": "string"
                }
            },
            {
                "name": "end_meeting",
                #"description": "结束会议",
                "parameters": {
                    "meeting_name": "string"
                }
            }
        ]

    def _create_function_calling_prompt(self, user_query: str) -> str:
        """创建函数调用提示词（简化版，一行格式）"""
        # 简化的函数列表JSON
        functions_json = json.dumps(self.functions, ensure_ascii=False)
        
        # 将提示词改为一行格式，去掉换行符
        prompt = f"你是一个只能调用函数的助手，必须根据用户的问题选择一个函数，并填写参数。你的回复必须严格按照以下格式返回，不能输出任何自然语言或解释说明： 你只能输出如下格式： {{ \"function_call\": {{ \"name\": \"函数名称\", \"arguments\": \"参数 JSON 字符串\" }} }} 可用函数列表： {functions_json} 用户问题：{user_query}"
        
        return prompt

    async def _get_oauth_token(self):
        """获取OAuth访问令牌"""
        global _access_token, _token_expires_at

        try:
            # 检查当前token是否还有效（提前5分钟刷新）
            current_time = time.time()
            if _access_token and current_time < (_token_expires_at - 300):
                logger.info("使用缓存的access_token")
                return _access_token

            # 获取OAuth配置
            base_url = self.config.get("JIUAN_API", "BASE_URL")
            token_path = self.config.get("JIUAN_API", "TOKEN_PATH")
            client_id = self.config.get("JIUAN_API", "CLIENT_ID")
            client_secret = self.config.get("JIUAN_API", "CLIENT_SECRET")
            verify_ssl = self.config.getboolean("JIUAN_API", "VERIFY_SSL", fallback=False)

            # 组合完整的token获取URL
            token_url = f"{base_url}{token_path}"

            logger.info(f"准备获取OAuth token: {token_url}")

            # 准备请求参数
            params = {
                "client_id": client_id,
                "client_secret": client_secret
            }

            # 发送请求获取token
            async with httpx.AsyncClient(verify=verify_ssl) as client:
                response = await client.get(token_url, params=params, timeout=30)

                if response.status_code != 200:
                    logger.error(f"获取OAuth token失败: {response.status_code}, {response.text}")
                    return None

                # 解析响应
                token_data = response.json()
                access_token = token_data.get("access_token")
                expires_in = token_data.get("expires_in", 3600)  # 默认1小时

                if access_token:
                    # 更新全局变量
                    _access_token = access_token
                    _token_expires_at = current_time + expires_in

                    logger.info("OAuth token获取成功")
                    return access_token
                else:
                    logger.error(f"响应中未找到access_token: {token_data}")
                    return None

        except Exception as e:
            logger.error(f"获取OAuth token时出错: {str(e)}")
            return None

    async def _get_conversation_id(self, access_token: str) -> Optional[str]:
        """获取久安大模型的会话ID"""
        try:
            # 获取配置信息
            base_url = self.config.get("JIUAN_API", "BASE_URL")
            conversation_path = self.config.get("JIUAN_API", "CONVERSATION_PATH")
            client_id = self.config.get("JIUAN_API", "CLIENT_ID")
            verify_ssl = self.config.getboolean("JIUAN_API", "VERIFY_SSL", fallback=False)

            # 构建获取会话ID的URL
            conversation_url = f"{base_url}{conversation_path}?client_id={client_id}&access_token={access_token}"

            logger.info(f"准备获取会话ID: {conversation_url}")

            # 准备请求头
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json"
            }

            # 准备请求体
            payload = {}

            # 发送请求获取会话ID
            async with httpx.AsyncClient(verify=verify_ssl) as client:
                response = await client.post(
                    conversation_url, 
                    json=payload,
                    headers=headers,
                    timeout=30
                )

                if response.status_code != 200:
                    logger.error(f"获取会话ID失败: {response.status_code}, {response.text}")
                    return None

                # 解析响应
                conversation_data = response.json()
                logger.info(f"会话ID响应: {json.dumps(conversation_data, ensure_ascii=False)[:100]}...")

                # 提取会话ID（根据API响应格式调整）
                conversation_id = conversation_data.get("conversation_id") or conversation_data.get("id") or conversation_data.get("data")

                if conversation_id:
                    logger.info(f"成功获取会话ID: {conversation_id}")
                    return conversation_id
                else:
                    logger.error(f"响应中未找到会话ID: {conversation_data}")
                    return None

        except Exception as e:
            logger.error(f"获取会话ID时出错: {str(e)}")
            return None

    async def _call_jiuan_model(self, prompt: str) -> str:
        """调用久安大模型"""
        try:
            # 获取OAuth token
            access_token = await self._get_oauth_token()
            if not access_token:
                logger.error("无法获取访问令牌")
                return ""

            # 获取有效的会话ID
            conversation_id = await self._get_conversation_id(access_token)
            if not conversation_id:
                logger.error("无法获取会话ID")
                return ""

            # 获取配置信息
            base_url = self.config.get("JIUAN_API", "BASE_URL")
            chat_path = self.config.get("JIUAN_API", "CHAT_PATH")
            client_id = self.config.get("JIUAN_API", "CLIENT_ID")
            verify_ssl = self.config.getboolean("JIUAN_API", "VERIFY_SSL", fallback=False)

            # 组合成完整URL，包含client_id和access_token参数
            url = f"{base_url}{chat_path}?client_id={client_id}&access_token={access_token}"

            # 准备请求头
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json"
            }

            # 准备请求体
            payload = {
                "query": prompt,
                "stream": False,
                "conversation_id": conversation_id
            }

            # 如果有app_id配置，添加到请求体中
            app_id = self.config.get("JIUAN_API", "APP_ID", fallback=None)
            if app_id and app_id.strip():
                payload["app_id"] = app_id

            logger.info(f"调用久安大模型API: {url}")
            logger.info(f"使用会话ID: {conversation_id}")

            # 发送请求
            async with httpx.AsyncClient(verify=verify_ssl) as client:
                response = await client.post(
                    url,
                    json=payload,
                    headers=headers,
                    timeout=120
                )

                # 检查响应状态
                if response.status_code != 200:
                    logger.error(f"API调用失败: {response.status_code}, {response.text}")
                    return ""

                # 解析响应内容
                response_data = response.json()
                logger.info(f"API返回: {json.dumps(response_data, ensure_ascii=False)[:200]}...")

                # 提取响应内容 - 修复：根据久安大模型的实际响应格式
                # 首先尝试从根级别提取answer字段
                content = response_data.get("answer", "")
                
                # 如果根级别没有answer，再尝试从data字段提取（兼容其他可能的格式）
                if not content and response_data.get("data"):
                    data = response_data["data"]
                    content = data.get("answer", "") or data.get("result", "") or data.get("msg", "")
                
                # 如果还是没有内容，尝试其他可能的字段
                if not content:
                    content = response_data.get("result", "") or response_data.get("msg", "") or response_data.get("text", "")

                return content

        except Exception as e:
            logger.error(f"调用久安大模型时出错: {str(e)}")
            return ""

    def _parse_function_call(self, response: str) -> Optional[Dict]:
        """解析函数调用响应"""
        try:
            # 清理响应文本
            response = response.strip()
            
            # 检查并去除markdown代码块标记
            if "```json" in response:
                # 使用正则表达式提取json代码块内容
                json_match = re.search(r'```json\s*\n(.*?)\n```', response, re.DOTALL)
                if json_match:
                    response = json_match.group(1).strip()
                    logger.info(f"从markdown代码块中提取到JSON: {response}")
                else:
                    # 如果正则匹配失败，尝试手动处理
                    start_marker = "```json"
                    end_marker = "```"
                    start_idx = response.find(start_marker)
                    if start_idx != -1:
                        start_idx += len(start_marker)
                        # 跳过换行符
                        while start_idx < len(response) and response[start_idx] in '\n\r':
                            start_idx += 1
                        
                        end_idx = response.find(end_marker, start_idx)
                        if end_idx != -1:
                            response = response[start_idx:end_idx].strip()
                            logger.info(f"手动提取到JSON: {response}")
            
            # 尝试直接解析JSON
            if response.startswith('{') and response.endswith('}'):
                return json.loads(response)

            # 尝试从响应中提取JSON
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())

            logger.error(f"无法解析函数调用响应: {response}")
            return None

        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}, 响应: {response}")
            return None
        except Exception as e:
            logger.error(f"解析函数调用时出错: {str(e)}, 响应: {response}")
            return None

    def _execute_function(self, function_call: Dict) -> str:
        """执行函数调用"""
        try:
            function_name = function_call.get("function_call", {}).get("name")
            arguments_str = function_call.get("function_call", {}).get("arguments", "{}")

            if not function_name:
                return "错误：未找到函数名称"

            # 解析参数
            try:
                arguments = json.loads(arguments_str) if isinstance(arguments_str, str) else arguments_str
            except json.JSONDecodeError:
                return f"错误：参数解析失败 - {arguments_str}"

            # 函数映射
            function_map = {
                "query_event_surround_resource": self._call_query_event_surround_resource,
                "preview_camera": self._call_preview_camera,
                "one_map_position_resource": self._call_one_map_position_resource,
                "event_stop_plan": self._call_event_stop_plan,
                "event_start_plan": self._call_event_start_plan,
                "query_all_event_infos": self._call_query_all_event_infos,
                "search_emergency_plan": self._call_search_emergency_plan,
                "query_area_plans": self._call_query_area_plans,
                "query_resource_cameras": self._call_query_resource_cameras,
                "start_call_by_name": self._call_start_call_by_name,
                "query_model_resources": self._call_query_model_resources,
                "start_real_time_travel": self._call_start_real_time_travel,
                "start_meeting": self._call_start_meeting,
                "end_meeting": self._call_end_meeting
            }

            if function_name not in function_map:
                return f"错误：未知函数 - {function_name}"

            # 执行函数
            return function_map[function_name](arguments)

        except Exception as e:
            logger.error(f"执行函数时出错: {str(e)}")
            return f"执行函数时出错: {str(e)}"

    # 函数调用包装器
    def _call_query_event_surround_resource(self, args: Dict) -> str:
        event_name = args.get("event_name", "")
        distance = args.get("distance", "")
        resource_type = args.get("resource_type", "")  # 可选参数
        #input_str = f"{event_name},{distance},{resource_type}"
        # 构建输入字符串，如果resource_type为空则不传递该参数
        if resource_type and resource_type.strip():
            input_str = f"{event_name},{distance},{resource_type}"
        else:
            input_str = f"{event_name},{distance},"  # 保持格式一致，但resource_type为空
        return query_event_surround_resource(input_str)

    def _call_preview_camera(self, args: Dict) -> str:
        camera_id = args.get("camera_id", "")
        return preview_camera(camera_id)

    def _call_one_map_position_resource(self, args: Dict) -> str:
        resource_name = args.get("resource_name", "")
        resource_type = args.get("resource_type", "")  # 可选参数
        input_str = f"{resource_name},{resource_type}" if resource_type else resource_name
        return one_map_position_resource(input_str)

    def _call_event_stop_plan(self, args: Dict) -> str:
        event_name = args.get("event_name", "")
        return event_stop_plan(event_name)

    def _call_event_start_plan(self, args: Dict) -> str:
        event_name = args.get("event_name", "")
        plan_name = args.get("plan_name", "")  # 可选参数
        input_str = f"{event_name},{plan_name}" if plan_name else event_name
        return event_start_plan(input_str)

    def _call_query_all_event_infos(self, args: Dict) -> str:
        page = args.get("page", 1)  # 默认值1
        page_size = args.get("page_size", 50)  # 默认值50
        event_status = args.get("event_status", "")  # 可选参数
        event_name = args.get("event_name", "")  # 可选参数
        event_level = args.get("event_level", "")  # 可选参数
        input_str = f"{page},{page_size},{event_status},{event_name},{event_level}"
        return query_all_event_infos(input_str)

    def _call_search_emergency_plan(self, args: Dict) -> str:
        plan_type = args.get("plan_type", "")  # 可选参数
        page = args.get("page", 1)  # 默认值1
        page_size = args.get("page_size", 20)  # 默认值20
        input_str = f"{plan_type},{page},{page_size}"
        return search_emergency_plan(input_str)

    def _call_query_area_plans(self, args: Dict) -> str:
        area_name = args.get("area_name", "")
        return query_area_plans(area_name)

    def _call_query_resource_cameras(self, args: Dict) -> str:
        resource_name = args.get("resource_name", "")
        resource_type = args.get("resource_type", "")
        input_str = f"{resource_name},{resource_type}"
        return query_resource_cameras(input_str)

    def _call_start_call_by_name(self, args: Dict) -> str:
        resource_name = args.get("resource_name", "")
        resource_type = args.get("resource_type", "")
        calling_type = args.get("calling_type", "")
        input_str = f"{resource_name},{resource_type},{calling_type}"
        return start_call_by_name(input_str)

    def _call_query_model_resources(self, args: Dict) -> str:
        model_name = args.get("model_name", "")
        return query_model_resources(model_name)

    def _call_start_real_time_travel(self, args: Dict) -> str:
        _ = args  # 忽略参数
        return start_real_time_travel()  # 这个函数不需要参数

    def _call_start_meeting(self, args: Dict) -> str:
        meeting_name = args.get("meeting_name", "")
        return start_meeting(meeting_name)

    def _call_end_meeting(self, args: Dict) -> str:
        meeting_name = args.get("meeting_name", "")
        return end_meeting(meeting_name)

    async def invoke_async(self, user_query: str) -> str:
        """异步处理用户查询"""
        try:
            # 创建函数调用提示词
            prompt = self._create_function_calling_prompt(user_query)
            logger.info(f"生成的提示词: {prompt}")

            # 调用久安大模型
            response = await self._call_jiuan_model(prompt)
            logger.info(f"久安大模型响应: {response}")

            if not response:
                return "错误：久安大模型无响应指挥命令智能体未识别，请规范命令，并重新输入指挥命令。"

            # 解析函数调用
            function_call = self._parse_function_call(response)
            if not function_call:
                return f"错误：无法解析函数调用 - {response}"

            logger.info(f"解析的函数调用: {function_call}")

            # 执行函数
            result = self._execute_function(function_call)
            logger.info(f"函数执行结果: {result}")

            return result

        except Exception as e:
            logger.error(f"处理用户查询时出错: {str(e)}")
            return f"处理查询时出错: {str(e)}"

def create_jiuan_agent() -> JiuanFunctionCallingAgent:
    """
    创建久安大模型函数调用代理
    """
    try:
        # 加载配置 - 使用主配置文件
        config = configparser.ConfigParser()
        config.read("config.ini", encoding='utf-8')

        # 创建代理
        agent = JiuanFunctionCallingAgent(config)

        logger.info("久安大模型函数调用代理创建成功")
        return agent

    except Exception as e:
        logger.error(f"创建久安大模型代理失败: {str(e)}")
        raise e

async def create_function_calling_agent(user_query: str) -> Optional[str]:
    """
    创建并使用函数调用代理处理用户查询（异步版本）
    """
    try:
        # 创建代理
        agent = create_jiuan_agent()

        # 异步处理查询
        result = await agent.invoke_async(user_query)

        return result

    except Exception as e:
        logger.error(f"函数调用代理处理失败: {str(e)}")
        return None
