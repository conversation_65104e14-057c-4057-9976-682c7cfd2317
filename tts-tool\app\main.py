"""
FastAPI主应用
"""
import logging
import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAPI, Request
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware

from app.api.endpoints import router as api_router
from app.core.config import get_settings
from app.core.tts_engine import tts_engine

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

settings = get_settings()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("正在启动TTS服务...")
    
    # 初始化TTS引擎
    success = await tts_engine.initialize()
    if not success:
        logger.error("TTS引擎初始化失败")
        raise RuntimeError("TTS引擎初始化失败")
    
    # 启动定期清理任务
    cleanup_task = asyncio.create_task(_periodic_cleanup())
    
    logger.info("TTS服务启动完成")
    
    yield
    
    # 关闭时执行
    logger.info("正在关闭TTS服务...")
    cleanup_task.cancel()
    try:
        await cleanup_task
    except asyncio.CancelledError:
        pass
    logger.info("TTS服务已关闭")


async def _periodic_cleanup():
    """定期清理任务"""
    while True:
        try:
            await asyncio.sleep(settings.cleanup_interval)
            await tts_engine.cleanup_temp_files()
            logger.debug("定期清理任务执行完成")
        except asyncio.CancelledError:
            break
        except Exception as e:
            logger.error(f"定期清理任务失败: {e}")


# 创建FastAPI应用
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="基于Kokoro TTS的文字转语音工具",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加可信主机中间件（可选）
if not settings.debug:
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["*"]  # 生产环境中应该限制具体主机
    )

# 注册API路由
app.include_router(api_router, prefix="/api", tags=["TTS API"])

# 挂载静态文件
app.mount("/static", StaticFiles(directory="app/static"), name="static")


@app.get("/", response_class=HTMLResponse)
async def read_root():
    """主页路由"""
    try:
        with open("app/static/index.html", "r", encoding="utf-8") as f:
            html_content = f.read()
        return HTMLResponse(content=html_content)
    except FileNotFoundError:
        return HTMLResponse(
            content="""
            <html>
                <head><title>TTS Tool</title></head>
                <body>
                    <h1>TTS工具</h1>
                    <p>前端页面正在开发中...</p>
                    <p>API文档: <a href="/docs">/docs</a></p>
                </body>
            </html>
            """,
            status_code=200
        )


@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": settings.app_name,
        "version": settings.app_version,
        "tts_engine_loaded": tts_engine.is_loaded
    }


@app.middleware("http")
async def log_requests(request: Request, call_next):
    """请求日志中间件"""
    start_time = asyncio.get_event_loop().time()
    
    # 处理请求
    response = await call_next(request)
    
    # 计算处理时间
    process_time = asyncio.get_event_loop().time() - start_time
    
    # 记录日志
    logger.info(
        f"{request.method} {request.url.path} - "
        f"Status: {response.status_code} - "
        f"Time: {process_time:.3f}s"
    )
    
    # 添加响应头
    response.headers["X-Process-Time"] = str(process_time)
    
    return response


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="info"
    )

