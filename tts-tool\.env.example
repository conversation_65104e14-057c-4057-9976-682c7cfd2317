# TTS工具环境配置示例
# 复制此文件为 .env 并根据需要修改配置

# 应用基本配置
APP_NAME=TTS Tool
APP_VERSION=1.0.0
DEBUG=false

# 服务器配置
HOST=0.0.0.0
PORT=8000

# TTS模型配置
MODEL_NAME=hexgrad/Kokoro-82M-v1.1-zh
MODEL_CACHE_DIR=./models

# 音频配置
SAMPLE_RATE=24000
AUDIO_FORMAT=wav
MAX_TEXT_LENGTH=1000

# 安全配置
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT=30

# 临时文件配置
TEMP_DIR=./temp
CLEANUP_INTERVAL=3600

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=./logs/tts-tool.log

# 数据库配置（如果需要）
# DATABASE_URL=sqlite:///./tts_tool.db

# Redis配置（如果需要缓存）
# REDIS_URL=redis://localhost:6379/0

# 监控配置
# SENTRY_DSN=your_sentry_dsn_here

# CORS配置
# ALLOWED_ORIGINS=["http://localhost:3000", "https://yourdomain.com"]

