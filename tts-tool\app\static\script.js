/**
 * TTS工具前端交互逻辑
 */

class TTSApp {
    constructor() {
        this.apiBase = '/api';
        this.currentAudioUrl = null;
        this.isGenerating = false;
        
        this.initializeElements();
        this.bindEvents();
        this.checkSystemStatus();
        this.loadVoices();
    }
    
    initializeElements() {
        // 获取DOM元素
        this.elements = {
            textInput: document.getElementById('textInput'),
            charCount: document.getElementById('charCount'),
            voiceSelect: document.getElementById('voiceSelect'),
            speedRange: document.getElementById('speedRange'),
            speedValue: document.getElementById('speedValue'),
            formatSelect: document.getElementById('formatSelect'),
            generateBtn: document.getElementById('generateBtn'),
            clearBtn: document.getElementById('clearBtn'),
            testBtn: document.getElementById('testBtn'),
            progressContainer: document.getElementById('progressContainer'),
            progressFill: document.getElementById('progressFill'),
            progressText: document.getElementById('progressText'),
            resultSection: document.getElementById('resultSection'),
            audioPlayer: document.getElementById('audioPlayer'),
            audioInfo: document.getElementById('audioInfo'),
            audioDuration: document.getElementById('audioDuration'),
            audioSize: document.getElementById('audioSize'),
            audioVoice: document.getElementById('audioVoice'),
            downloadBtn: document.getElementById('downloadBtn'),
            statusIndicator: document.getElementById('statusIndicator'),
            notification: document.getElementById('notification')
        };
    }
    
    bindEvents() {
        // 文本输入事件
        this.elements.textInput.addEventListener('input', () => {
            this.updateCharCount();
        });
        
        // 语速滑块事件
        this.elements.speedRange.addEventListener('input', (e) => {
            this.elements.speedValue.textContent = e.target.value;
        });
        
        // 按钮事件
        this.elements.generateBtn.addEventListener('click', () => {
            this.generateSpeech();
        });
        
        this.elements.clearBtn.addEventListener('click', () => {
            this.clearAll();
        });
        
        this.elements.testBtn.addEventListener('click', () => {
            this.testTTS();
        });
        
        this.elements.downloadBtn.addEventListener('click', () => {
            this.downloadAudio();
        });
        
        // 快速输入按钮事件
        document.querySelectorAll('.quick-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const text = e.target.getAttribute('data-text');
                this.elements.textInput.value = text;
                this.updateCharCount();
            });
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                this.generateSpeech();
            }
        });
    }
    
    updateCharCount() {
        const count = this.elements.textInput.value.length;
        this.elements.charCount.textContent = count;
        
        // 字符数颜色提示
        if (count > 800) {
            this.elements.charCount.style.color = 'var(--error-color)';
        } else if (count > 600) {
            this.elements.charCount.style.color = 'var(--warning-color)';
        } else {
            this.elements.charCount.style.color = 'var(--text-muted)';
        }
    }
    
    async checkSystemStatus() {
        try {
            const response = await fetch(`${this.apiBase}/status`);
            const data = await response.json();
            
            this.updateStatusIndicator(data);
            
            if (!data.model_loaded) {
                this.showNotification('系统正在初始化，请稍候...', 'warning');
                // 5秒后重新检查
                setTimeout(() => this.checkSystemStatus(), 5000);
            }
        } catch (error) {
            console.error('检查系统状态失败:', error);
            this.updateStatusIndicator({ status: 'error', model_loaded: false });
            this.showNotification('无法连接到服务器', 'error');
        }
    }
    
    updateStatusIndicator(status) {
        const dot = this.elements.statusIndicator.querySelector('.status-dot');
        const text = this.elements.statusIndicator.querySelector('.status-text');
        
        dot.className = 'status-dot';
        
        if (status.model_loaded && status.status === 'healthy') {
            dot.classList.add('healthy');
            text.textContent = '服务正常';
        } else if (status.status === 'error') {
            dot.classList.add('error');
            text.textContent = '服务异常';
        } else {
            text.textContent = '初始化中';
        }
    }
    
    async loadVoices() {
        try {
            const response = await fetch(`${this.apiBase}/voices`);
            const data = await response.json();
            
            this.elements.voiceSelect.innerHTML = '';
            
            data.voices.forEach(voice => {
                const option = document.createElement('option');
                option.value = voice.name;
                option.textContent = voice.display_name;
                this.elements.voiceSelect.appendChild(option);
            });
            
            // 默认选择第一个中文语音
            const chineseVoice = data.voices.find(v => v.language === 'zh');
            if (chineseVoice) {
                this.elements.voiceSelect.value = chineseVoice.name;
            }
            
        } catch (error) {
            console.error('加载语音列表失败:', error);
            this.elements.voiceSelect.innerHTML = '<option value="">加载失败</option>';
            this.showNotification('加载语音列表失败', 'error');
        }
    }
    
    async generateSpeech() {
        const text = this.elements.textInput.value.trim();
        
        if (!text) {
            this.showNotification('请输入要转换的文本', 'warning');
            return;
        }
        
        if (this.isGenerating) {
            return;
        }
        
        this.isGenerating = true;
        this.setGeneratingState(true);
        
        try {
            const requestData = {
                text: text,
                voice: this.elements.voiceSelect.value,
                speed: parseFloat(this.elements.speedRange.value),
                format: this.elements.formatSelect.value
            };
            
            this.showProgress('正在生成语音...', 20);
            
            const response = await fetch(`${this.apiBase}/tts`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });
            
            this.showProgress('处理中...', 60);
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || '生成失败');
            }
            
            this.showProgress('完成', 100);
            
            // 获取音频数据
            const audioBlob = await response.blob();
            const audioUrl = URL.createObjectURL(audioBlob);
            
            // 获取响应头信息
            const duration = response.headers.get('X-Audio-Duration');
            const size = response.headers.get('X-Audio-Size');
            const voice = response.headers.get('X-Voice-Used');
            
            this.displayResult(audioUrl, {
                duration: duration,
                size: size,
                voice: voice,
                format: requestData.format
            });
            
            this.showNotification('语音生成成功！', 'success');
            
        } catch (error) {
            console.error('生成语音失败:', error);
            this.showNotification(`生成失败: ${error.message}`, 'error');
        } finally {
            this.isGenerating = false;
            this.setGeneratingState(false);
            this.hideProgress();
        }
    }
    
    async testTTS() {
        try {
            this.showNotification('正在测试TTS功能...', 'info');
            
            const response = await fetch(`${this.apiBase}/test`, {
                method: 'POST'
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showNotification('TTS测试成功！', 'success');
            } else {
                this.showNotification(`TTS测试失败: ${data.message}`, 'error');
            }
            
        } catch (error) {
            console.error('TTS测试失败:', error);
            this.showNotification('TTS测试失败', 'error');
        }
    }
    
    setGeneratingState(generating) {
        this.elements.generateBtn.disabled = generating;
        this.elements.testBtn.disabled = generating;
        
        if (generating) {
            this.elements.generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>生成中...</span>';
        } else {
            this.elements.generateBtn.innerHTML = '<i class="fas fa-play"></i><span>生成语音</span>';
        }
    }
    
    showProgress(text, percentage) {
        this.elements.progressContainer.style.display = 'block';
        this.elements.progressText.textContent = text;
        this.elements.progressFill.style.width = `${percentage}%`;
    }
    
    hideProgress() {
        setTimeout(() => {
            this.elements.progressContainer.style.display = 'none';
            this.elements.progressFill.style.width = '0%';
        }, 1000);
    }
    
    displayResult(audioUrl, metadata) {
        // 清理之前的音频URL
        if (this.currentAudioUrl) {
            URL.revokeObjectURL(this.currentAudioUrl);
        }
        
        this.currentAudioUrl = audioUrl;
        
        // 设置音频播放器
        this.elements.audioPlayer.src = audioUrl;
        
        // 更新音频信息
        this.elements.audioDuration.textContent = metadata.duration ? 
            `${parseFloat(metadata.duration).toFixed(1)}秒` : '--';
        this.elements.audioSize.textContent = metadata.size ? 
            this.formatFileSize(parseInt(metadata.size)) : '--';
        this.elements.audioVoice.textContent = metadata.voice || '--';
        
        // 显示结果区域
        this.elements.resultSection.style.display = 'block';
        this.elements.resultSection.scrollIntoView({ behavior: 'smooth' });
    }
    
    downloadAudio() {
        if (!this.currentAudioUrl) {
            this.showNotification('没有可下载的音频', 'warning');
            return;
        }
        
        const format = this.elements.formatSelect.value;
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const filename = `tts_${timestamp}.${format}`;
        
        const a = document.createElement('a');
        a.href = this.currentAudioUrl;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        this.showNotification('音频下载已开始', 'success');
    }
    
    clearAll() {
        this.elements.textInput.value = '';
        this.updateCharCount();
        this.elements.resultSection.style.display = 'none';
        
        if (this.currentAudioUrl) {
            URL.revokeObjectURL(this.currentAudioUrl);
            this.currentAudioUrl = null;
        }
        
        this.showNotification('已清空所有内容', 'info');
    }
    
    showNotification(message, type = 'info') {
        const notification = this.elements.notification;
        const icon = notification.querySelector('.notification-icon');
        const messageEl = notification.querySelector('.notification-message');
        
        // 设置图标
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };
        
        icon.className = `notification-icon ${icons[type]}`;
        messageEl.textContent = message;
        
        // 设置样式
        notification.className = `notification ${type}`;
        
        // 显示通知
        notification.classList.add('show');
        
        // 自动隐藏
        setTimeout(() => {
            notification.classList.remove('show');
        }, 4000);
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.ttsApp = new TTSApp();
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    if (window.ttsApp && window.ttsApp.currentAudioUrl) {
        URL.revokeObjectURL(window.ttsApp.currentAudioUrl);
    }
});

