#!/usr/bin/env python3
"""
TTS工具启动脚本
"""
import os
import sys
import uvicorn
from app.core.config import get_settings

def main():
    """主函数"""
    settings = get_settings()
    
    print(f"启动 {settings.app_name} v{settings.app_version}")
    print(f"服务地址: http://{settings.host}:{settings.port}")
    print(f"API文档: http://{settings.host}:{settings.port}/docs")
    print("按 Ctrl+C 停止服务")
    print("-" * 50)
    
    try:
        uvicorn.run(
            "app.main:app",
            host=settings.host,
            port=settings.port,
            reload=settings.debug,
            log_level="info",
            access_log=True
        )
    except KeyboardInterrupt:
        print("\n服务已停止")
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

