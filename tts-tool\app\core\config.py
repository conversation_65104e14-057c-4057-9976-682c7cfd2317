"""
配置管理模块
"""
import os
from typing import Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置"""
    
    # 应用基本配置
    app_name: str = "TTS Tool"
    app_version: str = "1.0.0"
    debug: bool = False
    
    # 服务器配置
    host: str = "0.0.0.0"
    port: int = 8000
    
    # TTS模型配置
    model_name: str = "hexgrad/Kokoro-82M-v1.1-zh"  # 模型目录名
    model_cache_dir: str = "./models"  # 模型根目录
    model_path: str = "./models/hexgrad/Kokoro-82M-v1.1-zh/kokoro-v1_1-zh.pth"  # 本地模型文件路径
    voices_dir: str = "./models/hexgrad/Kokoro-82M-v1.1-zh/voices"  # 本地voices目录
    
    # 音频配置
    sample_rate: int = 24000
    audio_format: str = "wav"
    max_text_length: int = 1000
    
    # 安全配置
    max_concurrent_requests: int = 10
    request_timeout: int = 30
    
    # 临时文件配置
    temp_dir: str = "./temp"
    cleanup_interval: int = 3600  # 1小时清理一次临时文件
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# 全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取配置实例"""
    return settings

