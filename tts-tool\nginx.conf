events {
    worker_connections 1024;
}

http {
    upstream tts_backend {
        server tts-tool:8000;
    }

    server {
        listen 80;
        server_name localhost;

        # 设置客户端最大请求体大小
        client_max_body_size 10M;

        # 设置超时时间
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # 主页和静态文件
        location / {
            proxy_pass http://tts_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # API端点
        location /api/ {
            proxy_pass http://tts_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 增加超时时间用于TTS处理
            proxy_read_timeout 120s;
            proxy_send_timeout 120s;
        }

        # 健康检查
        location /health {
            proxy_pass http://tts_backend;
            access_log off;
        }

        # 静态文件缓存
        location /static/ {
            proxy_pass http://tts_backend;
            proxy_cache_valid 200 1h;
            add_header Cache-Control "public, max-age=3600";
        }

        # 错误页面
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }
}

