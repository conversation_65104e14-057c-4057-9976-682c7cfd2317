# TTS工具 - 基于Kokoro TTS的文字转语音系统

一个现代化的文字转语音工具，基于开源的Kokoro TTS模型，支持中文语音合成，提供完整的Web界面和API接口。

## 🌟 特性

- **开源可商用**: 基于Apache 2.0许可证的Kokoro TTS模型
- **中文优化**: 专门优化的中文语音合成，发音流畅自然
- **轻量高效**: 仅82M参数的模型，运行效率高
- **现代化界面**: 响应式Web界面，支持桌面和移动设备
- **完整API**: RESTful API接口，支持集成到其他应用
- **多语音支持**: 支持多种中文和英文语音
- **实时处理**: 快速语音合成，支持实时应用

## 🚀 快速开始

### 环境要求

- Python 3.8+
- 2GB+ 内存
- 1GB+ 存储空间

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd tts-tool
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **启动服务**
```bash
python run.py
```

4. **访问应用**
- Web界面: http://localhost:8000
- API文档: http://localhost:8000/docs

## 📖 使用说明

### Web界面使用

1. 在文本输入框中输入要转换的文本
2. 选择合适的语音和语速
3. 点击"生成语音"按钮
4. 播放或下载生成的音频文件

### API使用

#### 文本转语音
```bash
curl -X POST "http://localhost:8000/api/tts" \
     -H "Content-Type: application/json" \
     -d '{
       "text": "你好，欢迎使用TTS工具！",
       "voice": "zh_female_001",
       "speed": 1.0,
       "format": "wav"
     }' \
     --output audio.wav
```

#### 获取可用语音
```bash
curl "http://localhost:8000/api/voices"
```

#### 检查系统状态
```bash
curl "http://localhost:8000/api/status"
```

## 🏗️ 项目结构

```
tts-tool/
├── app/                    # 应用主目录
│   ├── api/               # API端点
│   │   └── endpoints.py   # API路由定义
│   ├── core/              # 核心模块
│   │   ├── config.py      # 配置管理
│   │   └── tts_engine.py  # TTS引擎
│   ├── models/            # 数据模型
│   │   └── schemas.py     # Pydantic模型
│   ├── static/            # 静态文件
│   │   ├── index.html     # 前端页面
│   │   ├── style.css      # 样式文件
│   │   └── script.js      # JavaScript逻辑
│   └── main.py            # FastAPI应用
├── models/                # 模型文件目录
├── temp/                  # 临时文件目录
├── requirements.txt       # 依赖包列表
├── run.py                # 启动脚本
└── README.md             # 项目说明
```

## ⚙️ 配置说明

### 环境变量

可以通过环境变量或`.env`文件配置应用：

```bash
# 服务器配置
HOST=0.0.0.0
PORT=8000
DEBUG=false

# TTS模型配置
MODEL_NAME=hexgrad/Kokoro-82M-v1.1-zh
MODEL_CACHE_DIR=./models

# 音频配置
SAMPLE_RATE=24000
MAX_TEXT_LENGTH=1000

# 安全配置
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT=30
```

### 配置文件

主要配置在 `app/core/config.py` 中定义，包括：

- 服务器设置（主机、端口）
- TTS模型配置
- 音频参数
- 安全限制
- 临时文件管理

## 🔧 开发指南

### 本地开发

1. **安装开发依赖**
```bash
pip install -r requirements.txt
```

2. **启动开发服务器**
```bash
python run.py
```

3. **代码结构说明**
- `app/main.py`: FastAPI应用入口
- `app/api/endpoints.py`: API端点定义
- `app/core/tts_engine.py`: TTS引擎核心逻辑
- `app/models/schemas.py`: 数据模型定义
- `app/static/`: 前端静态文件

### 添加新语音

1. 在 `tts_engine.py` 的 `get_available_voices()` 方法中添加新语音
2. 确保TTS模型支持该语音
3. 更新前端语音选择器

### 自定义TTS模型

1. 修改 `app/core/tts_engine.py` 中的模型加载逻辑
2. 更新 `requirements.txt` 中的依赖
3. 调整配置文件中的模型参数

## 🚀 部署指南

### Docker部署

1. **构建镜像**
```bash
docker build -t tts-tool .
```

2. **运行容器**
```bash
docker run -p 8000:8000 tts-tool
```

### 生产环境部署

1. **使用Gunicorn**
```bash
pip install gunicorn
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

2. **使用Nginx反向代理**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

3. **使用Systemd服务**
```ini
[Unit]
Description=TTS Tool
After=network.target

[Service]
Type=exec
User=www-data
WorkingDirectory=/path/to/tts-tool
ExecStart=/usr/bin/python3 run.py
Restart=always

[Install]
WantedBy=multi-user.target
```

## 📊 性能优化

### 系统要求

- **最低配置**: 2GB RAM, 1GB 存储
- **推荐配置**: 4GB RAM, 2GB 存储
- **高并发**: 8GB+ RAM, SSD存储

### 优化建议

1. **并发控制**: 调整 `MAX_CONCURRENT_REQUESTS` 参数
2. **缓存策略**: 启用模型缓存和音频缓存
3. **资源监控**: 监控内存和CPU使用情况
4. **负载均衡**: 多实例部署提高并发能力

## 🔒 安全考虑

### 输入验证

- 文本长度限制（默认1000字符）
- 特殊字符过滤
- 请求频率限制

### 资源保护

- 内存使用监控
- 并发请求限制
- 临时文件自动清理

### 网络安全

- CORS配置
- 请求头验证
- HTTPS支持（生产环境）

## 🐛 故障排除

### 常见问题

1. **模型加载失败**
   - 检查网络连接
   - 确认模型缓存目录权限
   - 查看日志文件

2. **音频生成失败**
   - 检查文本内容是否有效
   - 确认语音参数正确
   - 验证临时目录权限

3. **服务启动失败**
   - 检查端口是否被占用
   - 确认Python版本兼容性
   - 验证依赖包安装

### 日志查看

```bash
# 查看应用日志
tail -f /var/log/tts-tool.log

# 查看系统日志
journalctl -u tts-tool -f
```

## 🤝 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目基于 MIT 许可证开源 - 查看 [LICENSE](LICENSE) 文件了解详情。

使用的TTS模型 Kokoro TTS 基于 Apache 2.0 许可证，支持商业使用。

## 🙏 致谢

- [Kokoro TTS](https://kokorotts.net/) - 提供优秀的开源TTS模型
- [FastAPI](https://fastapi.tiangolo.com/) - 现代化的Python Web框架
- [StyleTTS2](https://github.com/yl4579/StyleTTS2) - TTS模型架构基础

## 📞 支持

如有问题或建议，请：

1. 查看 [FAQ](docs/FAQ.md)
2. 提交 [Issue](https://github.com/your-repo/issues)
3. 联系开发团队

---

**TTS工具** - 让文字转语音变得简单高效！

