"""
TTS引擎模块 - 基于Kokoro TTS
"""
import os
import asyncio
import tempfile
import logging
from typing import Dict, List, Optional, Tuple
from pathlib import Path
import soundfile as sf
import numpy as np
import torch

try:
    from kokoro import KModel, KPipeline
    from misaki.zh import ZHG2P as Misaki  # 使用正确的中文处理器
except ImportError:
    # 如果导入失败，提供模拟实现用于开发测试
    class KModel:
        def __init__(self, *args, **kwargs):
            pass
    
    class KPipeline:
        def __init__(self, *args, **kwargs):
            pass
        
        def __call__(self, *args, **kwargs):
            # 返回模拟的音频数据
            return np.random.randn(24000), 24000
    
    class Misaki:
        def __init__(self, *args, **kwargs):
            pass
        
        def __call__(self, *args, **kwargs):
            # 返回处理后的中文文本（模拟）
            return ("ni hao", None)

from app.core.config import get_settings
from app.models.schemas import Voice

logger = logging.getLogger(__name__)


class TTSEngine:
    """TTS引擎类"""
    
    def __init__(self):
        self.settings = get_settings()
        self.model: Optional[KModel] = None
        self.pipeline: Optional[KPipeline] = None
        self.misaki: Optional[Misaki] = None
        self.is_loaded = False
        self._voices_cache: Optional[List[Voice]] = None
        
    async def initialize(self) -> bool:
        """初始化TTS引擎"""
        try:
            logger.info("正在初始化TTS引擎...")
            
            # 创建必要的目录
            os.makedirs(self.settings.model_cache_dir, exist_ok=True)
            os.makedirs(self.settings.temp_dir, exist_ok=True)
            
            # 在线程池中加载模型，避免阻塞事件循环
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, self._load_models)
            
            self.is_loaded = True
            logger.info("TTS引擎初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"TTS引擎初始化失败: {e}")
            return False
    
    def _load_models(self):
        """加载模型（在线程池中执行）"""
        try:
            # 检查本地模型文件是否存在
            if not os.path.exists(self.settings.model_path):
                logger.error(f"本地模型文件不存在: {self.settings.model_path}")
                # 如果本地文件不存在，回退到从HuggingFace加载
                logger.info("尝试从HuggingFace加载模型...")
                self.model = KModel(repo_id=self.settings.model_name)
            else:
                # 使用本地模型文件
                logger.info(f"从本地加载模型: {self.settings.model_path}")
                self.model = KModel(model=self.settings.model_path)
            
            # 创建推理管道，传入语言代码和模型
            self.pipeline = KPipeline(
                lang_code="zh",
                model=self.model
            )
            
            # 加载中文文本处理器
            self.misaki = Misaki()
            
            logger.info("模型加载完成")
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
    
    async def synthesize(
        self, 
        text: str, 
        voice: str = "zf_001",  # 使用实际存在的中文女声作为默认
        speed: float = 1.0,
        format: str = "wav"
    ) -> Tuple[str, Dict]:
        """
        合成语音
        
        Args:
            text: 要合成的文本
            voice: 语音名称
            speed: 语速倍率
            format: 音频格式
            
        Returns:
            Tuple[音频文件路径, 元数据]
        """
        if not self.is_loaded:
            raise RuntimeError("TTS引擎未初始化")
        
        try:
            # 在线程池中执行TTS合成
            loop = asyncio.get_event_loop()
            audio_data, sample_rate, metadata = await loop.run_in_executor(
                None, self._synthesize_audio, text, voice, speed
            )
            
            # 保存音频文件
            temp_file = await self._save_audio(audio_data, sample_rate, format)
            
            # 更新元数据
            metadata.update({
                "file_path": temp_file,
                "format": format,
                "sample_rate": sample_rate,
                "duration": len(audio_data) / sample_rate,
                "file_size": os.path.getsize(temp_file)
            })
            
            return temp_file, metadata
            
        except Exception as e:
            logger.error(f"语音合成失败: {e}")
            raise
    
    def _synthesize_audio(
        self, 
        text: str, 
        voice: str, 
        speed: float
    ) -> Tuple[np.ndarray, int, Dict]:
        """执行音频合成（在线程池中执行）"""
        try:
            # 预处理中文文本
            if self._is_chinese_text(text):
                # ZHG2P返回元组(phonetic_text, tones)，我们需要第一个元素
                result = self.misaki(text)
                processed_text = result[0] if isinstance(result, tuple) else result
            else:
                processed_text = text
            
            # 加载本地语音张量文件
            voice_file_path = os.path.join(self.settings.voices_dir, f"{voice}.pt")
            
            if not os.path.exists(voice_file_path):
                logger.error(f"本地语音文件不存在: {voice_file_path}")
                # 如果本地文件不存在，回退到使用字符串名称
                logger.warning(f"回退到使用字符串语音名称: {voice}")
                voice_tensor = voice
            else:
                # 加载语音张量文件
                logger.info(f"正在加载语音文件: {voice_file_path}")
                voice_tensor = torch.load(voice_file_path, map_location="cpu")
                logger.info(f"语音文件加载成功，类型: {type(voice_tensor)}")
            
            # 使用Kokoro进行语音合成
            # KPipeline返回生成器，需要收集所有结果
            results = list(self.pipeline(
                processed_text,
                voice=voice_tensor,  # 传入加载好的voice_tensor或字符串名称
                speed=speed
            ))
            
            # 合并所有音频片段
            if results:
                # Kokoro的默认采样率是24000Hz
                sample_rate = 24000
                audio_segments = [result.audio.numpy() if hasattr(result.audio, 'numpy') else result.audio 
                                  for result in results]
                audio_data = np.concatenate(audio_segments) if len(audio_segments) > 1 else audio_segments[0]
            else:
                # 如果没有结果，返回空音频
                sample_rate = self.settings.sample_rate
                audio_data = np.array([])
            
            # 确保音频数据是numpy数组
            if not isinstance(audio_data, np.ndarray):
                audio_data = np.array(audio_data)
            
            metadata = {
                "original_text": text,
                "processed_text": processed_text,
                "voice": voice,
                "voice_file": voice_file_path if os.path.exists(voice_file_path) else "fallback",
                "speed": speed
            }
            
            return audio_data, sample_rate, metadata
            
        except Exception as e:
            logger.error(f"音频合成执行失败: {e}")
            raise
    
    async def _save_audio(
        self, 
        audio_data: np.ndarray, 
        sample_rate: int, 
        format: str
    ) -> str:
        """保存音频文件"""
        try:
            # 创建临时文件
            suffix = f".{format}"
            temp_file = tempfile.NamedTemporaryFile(
                delete=False, 
                suffix=suffix, 
                dir=self.settings.temp_dir
            )
            temp_file.close()
            
            # 在线程池中保存文件
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None, 
                sf.write, 
                temp_file.name, 
                audio_data, 
                sample_rate
            )
            
            return temp_file.name
            
        except Exception as e:
            logger.error(f"音频文件保存失败: {e}")
            raise
    
    def _is_chinese_text(self, text: str) -> bool:
        """检查文本是否包含中文"""
        for char in text:
            if '\u4e00' <= char <= '\u9fff':
                return True
        return False
    
    async def get_available_voices(self) -> List[Voice]:
        """获取可用语音列表"""
        if self._voices_cache is not None:
            return self._voices_cache
        
        # 定义可用的语音
        voices = [
            # 中文女声 (基于实际的.pt文件)
            Voice(
                name="zf_001",
                display_name="中文女声-001",
                language="zh",
                gender="female",
                description="高质量中文女声-001"
            ),
            Voice(
                name="zf_002", 
                display_name="中文女声-002",
                language="zh",
                gender="female",
                description="高质量中文女声-002"
            ),
            Voice(
                name="zf_003",
                display_name="中文女声-003",
                language="zh",
                gender="female",
                description="高质量中文女声-003"
            ),
            Voice(
                name="zf_004",
                display_name="中文女声-004",
                language="zh",
                gender="female",
                description="高质量中文女声-004"
            ),
            Voice(
                name="zf_005",
                display_name="中文女声-005",
                language="zh",
                gender="female",
                description="高质量中文女声-005"
            ),
            # 中文男声
            Voice(
                name="zm_009",
                display_name="中文男声-009", 
                language="zh",
                gender="male",
                description="高质量中文男声-009"
            ),
            Voice(
                name="zm_010",
                display_name="中文男声-010",
                language="zh",
                gender="male",
                description="高质量中文男声-010"
            ),
            Voice(
                name="zm_011",
                display_name="中文男声-011",
                language="zh",
                gender="male",
                description="高质量中文男声-011"
            ),
            Voice(
                name="zm_012",
                display_name="中文男声-012",
                language="zh",
                gender="male",
                description="高质量中文男声-012"
            ),
            # 英文语音 (高质量)
            Voice(
                name="af_maple",
                display_name="英文女声-Maple",
                language="en",
                gender="female", 
                description="高质量英文女声-Maple"
            ),
            Voice(
                name="af_sol",
                display_name="英文女声-Sol",
                language="en",
                gender="female", 
                description="高质量英文女声-Sol"
            ),
            Voice(
                name="bf_vale",
                display_name="英文女声-Vale (英式)",
                language="en",
                gender="female", 
                description="高质量英式女声-Vale"
            )
        ]
        
        self._voices_cache = voices
        return voices
    
    async def cleanup_temp_files(self):
        """清理临时文件"""
        try:
            temp_dir = Path(self.settings.temp_dir)
            if temp_dir.exists():
                for file_path in temp_dir.glob("*.wav"):
                    try:
                        # 删除超过1小时的文件
                        if (asyncio.get_event_loop().time() - file_path.stat().st_mtime) > 3600:
                            file_path.unlink()
                    except Exception as e:
                        logger.warning(f"删除临时文件失败 {file_path}: {e}")
        except Exception as e:
            logger.error(f"清理临时文件失败: {e}")


# 全局TTS引擎实例
tts_engine = TTSEngine()


