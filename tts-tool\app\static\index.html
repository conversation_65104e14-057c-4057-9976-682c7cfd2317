<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTS工具 - 文字转语音</title>
    <link rel="stylesheet" href="/static/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <div class="header-content">
                <h1 class="title">
                    <i class="fas fa-microphone-alt"></i>
                    TTS工具
                </h1>
                <p class="subtitle">基于Kokoro TTS的智能文字转语音工具</p>
            </div>
            <div class="status-indicator" id="statusIndicator">
                <span class="status-dot"></span>
                <span class="status-text">检查中...</span>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 输入区域 -->
            <section class="input-section">
                <div class="card">
                    <h2 class="section-title">
                        <i class="fas fa-edit"></i>
                        文本输入
                    </h2>
                    
                    <div class="textarea-container">
                        <textarea 
                            id="textInput" 
                            placeholder="请输入要转换为语音的文本内容..."
                            maxlength="1000"
                        ></textarea>
                        <div class="char-counter">
                            <span id="charCount">0</span>/1000
                        </div>
                    </div>

                    <!-- 快速输入按钮 -->
                    <div class="quick-inputs">
                        <button class="quick-btn" data-text="你好，欢迎使用TTS工具！">示例1</button>
                        <button class="quick-btn" data-text="今天天气真不错，适合出去走走。">示例2</button>
                        <button class="quick-btn" data-text="人工智能技术正在改变我们的生活方式。">示例3</button>
                    </div>
                </div>
            </section>

            <!-- 设置区域 -->
            <section class="settings-section">
                <div class="card">
                    <h2 class="section-title">
                        <i class="fas fa-cog"></i>
                        语音设置
                    </h2>
                    
                    <div class="settings-grid">
                        <div class="setting-item">
                            <label for="voiceSelect">选择语音</label>
                            <select id="voiceSelect" class="select-input">
                                <option value="">加载中...</option>
                            </select>
                        </div>
                        
                        <div class="setting-item">
                            <label for="speedRange">语速 (<span id="speedValue">1.0</span>x)</label>
                            <input 
                                type="range" 
                                id="speedRange" 
                                min="0.5" 
                                max="2.0" 
                                step="0.1" 
                                value="1.0"
                                class="range-input"
                            >
                        </div>
                        
                        <div class="setting-item">
                            <label for="formatSelect">音频格式</label>
                            <select id="formatSelect" class="select-input">
                                <option value="wav">WAV</option>
                                <option value="mp3">MP3</option>
                            </select>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 控制区域 -->
            <section class="control-section">
                <div class="card">
                    <div class="control-buttons">
                        <button id="generateBtn" class="btn btn-primary">
                            <i class="fas fa-play"></i>
                            <span>生成语音</span>
                        </button>
                        
                        <button id="clearBtn" class="btn btn-secondary">
                            <i class="fas fa-trash"></i>
                            <span>清空</span>
                        </button>
                        
                        <button id="testBtn" class="btn btn-info">
                            <i class="fas fa-vial"></i>
                            <span>测试</span>
                        </button>
                    </div>
                    
                    <!-- 进度条 -->
                    <div class="progress-container" id="progressContainer" style="display: none;">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        <div class="progress-text" id="progressText">处理中...</div>
                    </div>
                </div>
            </section>

            <!-- 结果区域 -->
            <section class="result-section" id="resultSection" style="display: none;">
                <div class="card">
                    <h2 class="section-title">
                        <i class="fas fa-volume-up"></i>
                        生成结果
                    </h2>
                    
                    <div class="audio-player-container">
                        <audio id="audioPlayer" controls class="audio-player">
                            您的浏览器不支持音频播放。
                        </audio>
                        
                        <div class="audio-info" id="audioInfo">
                            <div class="info-item">
                                <span class="info-label">时长:</span>
                                <span class="info-value" id="audioDuration">--</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">大小:</span>
                                <span class="info-value" id="audioSize">--</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">语音:</span>
                                <span class="info-value" id="audioVoice">--</span>
                            </div>
                        </div>
                        
                        <button id="downloadBtn" class="btn btn-success">
                            <i class="fas fa-download"></i>
                            <span>下载音频</span>
                        </button>
                    </div>
                </div>
            </section>
        </main>

        <!-- 页脚 -->
        <footer class="footer">
            <div class="footer-content">
                <p>&copy; 2025 TTS工具. 基于 <a href="https://kokorotts.net" target="_blank">Kokoro TTS</a> 开发</p>
                <div class="footer-links">
                    <a href="/docs" target="_blank">API文档</a>
                    <a href="/api/status" target="_blank">系统状态</a>
                </div>
            </div>
        </footer>
    </div>

    <!-- 通知组件 -->
    <div id="notification" class="notification">
        <div class="notification-content">
            <i class="notification-icon"></i>
            <span class="notification-message"></span>
        </div>
    </div>

    <script src="/static/script.js"></script>
</body>
</html>

